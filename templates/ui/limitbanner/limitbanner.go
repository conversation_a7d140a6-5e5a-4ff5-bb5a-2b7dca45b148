package limitbanner

import (
	"fmt"

	"github.com/j-em/coachpad/i18n"
	"github.com/j-em/coachpad/pkg/limits"
	"github.com/j-em/coachpad/templates/ui/icons"
	"maragu.dev/gomponents"
	"maragu.dev/gomponents/html"
)

// LimitBannerConfig contains configuration for the limit banner
type LimitBannerConfig struct {
	LimitResult *limits.LimitCheckResult
	Lang        string
	ShowUpgrade bool
	UpgradeURL  string
	DataTestID  string
	ID          string
	Class       string
}

// LimitBanner renders a banner showing current usage and limits
func LimitBanner(config LimitBannerConfig) gomponents.Node {
	// Load locales
	locales, err := i18n.LoadTemplateLocales("./templates/ui/limitbanner/limitbanner.locales.json", config.Lang)
	if err != nil {
		locales = make(map[string]string)
	}

	if config.LimitResult == nil {
		return gomponents.Text("")
	}

	result := config.LimitResult

	// Don't show banner for unlimited resources
	if result.IsUnlimited {
		return gomponents.Text("")
	}

	// Calculate usage percentage
	usagePercentage := float64(result.CurrentCount) / float64(result.Limit) * 100

	// Determine banner style based on usage
	var bannerClass, iconColor, progressBarColor string
	var icon gomponents.Node

	if usagePercentage >= 100 {
		// At limit - red/danger
		bannerClass = "bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800"
		iconColor = "text-red-500 dark:text-red-400"
		progressBarColor = "bg-red-500"
		icon = icons.ExclamationCircle()
	} else if usagePercentage >= 80 {
		// Near limit - orange/warning
		bannerClass = "bg-orange-50 dark:bg-orange-900/20 border-orange-200 dark:border-orange-800"
		iconColor = "text-orange-500 dark:text-orange-400"
		progressBarColor = "bg-orange-500"
		icon = icons.ExclamationCircle()
	} else if usagePercentage >= 60 {
		// Approaching limit - blue/info
		bannerClass = "bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800"
		iconColor = "text-blue-500 dark:text-blue-400"
		progressBarColor = "bg-blue-500"
		icon = icons.InformationCircle()
	} else {
		// Well under limit - don't show banner
		return gomponents.Text("")
	}

	// Determine main message
	var message string
	if usagePercentage >= 100 {
		message = fmt.Sprintf(locales["limit_reached"], result.CurrentCount, result.Limit, result.ResourceType)
	} else {
		remaining := result.RemainingCapacity()
		message = fmt.Sprintf(locales["usage_status"], result.CurrentCount, result.Limit, result.ResourceType, remaining)
	}

	return html.Div(
		// Apply custom ID if provided
		gomponents.If(config.ID != "", html.ID(config.ID)),
		// Apply custom class if provided, otherwise use default spacing
		html.Class(fmt.Sprintf("rounded-lg border p-4 mb-4 %s %s", bannerClass, config.Class)),
		// Apply test ID if provided
		gomponents.If(config.DataTestID != "", gomponents.Attr("data-testid", config.DataTestID)),

		html.Div(
			html.Class("flex items-start space-x-3"),

			// Icon
			html.Div(
				html.Class(fmt.Sprintf("flex-shrink-0 %s", iconColor)),
				icon,
			),

			// Content
			html.Div(
				html.Class("flex-1 min-w-0"),

				// Main message
				html.P(
					html.Class("text-sm font-medium text-gray-900 dark:text-gray-100"),
					gomponents.Text(message),
				),

				// Progress bar
				html.Div(
					html.Class("mt-2"),
					html.Div(
						html.Class("bg-gray-200 dark:bg-gray-700 rounded-full h-2"),
						html.Div(
							html.Class(fmt.Sprintf("h-2 rounded-full transition-all duration-300 %s", progressBarColor)),
							gomponents.Attr("style", fmt.Sprintf("width: %.1f%%", usagePercentage)),
						),
					),
				),

				// Usage text under progress bar
				html.P(
					html.Class("text-xs text-gray-500 dark:text-gray-400 mt-1"),
					gomponents.Text(fmt.Sprintf("%.1f%% %s", usagePercentage, locales["of_limit_used"])),
				),
			),

			// Upgrade button (if enabled and user is near/at limit)
			gomponents.If(config.ShowUpgrade && usagePercentage >= 80,
				html.Div(
					html.Class("flex-shrink-0"),
					html.A(
						html.Href(config.UpgradeURL),
						html.Class("inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"),
						gomponents.Attr("data-testid", "upgrade-button"),
						icons.ArrowDownTray(),
						html.Span(html.Class("ml-1"), gomponents.Text(locales["upgrade_to_pro"])),
					),
				),
			),
		),
	)
}

// UsageIndicator renders a simple inline usage indicator (e.g., "15/20 players")
func UsageIndicator(config LimitBannerConfig) gomponents.Node {
	if config.LimitResult == nil {
		return gomponents.Text("")
	}

	result := config.LimitResult

	if result.IsUnlimited {
		return html.Span(
			html.Class("text-sm text-gray-500 dark:text-gray-400"),
			gomponents.Text(fmt.Sprintf("%d %s", result.CurrentCount, result.ResourceType)),
		)
	}

	// Calculate usage percentage for color coding
	usagePercentage := float64(result.CurrentCount) / float64(result.Limit) * 100

	var textColor string
	if usagePercentage >= 100 {
		textColor = "text-red-600 dark:text-red-400"
	} else if usagePercentage >= 80 {
		textColor = "text-orange-600 dark:text-orange-400"
	} else {
		textColor = "text-gray-500 dark:text-gray-400"
	}

	return html.Span(
		html.Class(fmt.Sprintf("text-sm %s", textColor)),
		gomponents.Text(fmt.Sprintf("%d/%d %s", result.CurrentCount, result.Limit, result.ResourceType)),
	)
}

// QuickUsageCard renders a card showing usage for multiple resource types
func QuickUsageCard(usage map[limits.ResourceType]*limits.LimitCheckResult, lang string) gomponents.Node {
	// Load locales
	locales, err := i18n.LoadTemplateLocales("./templates/ui/limitbanner/limitbanner.locales.json", lang)
	if err != nil {
		locales = make(map[string]string)
	}

	if len(usage) == 0 {
		return gomponents.Text("")
	}

	return html.Div(
		html.Class("bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4"),
		gomponents.Attr("data-testid", "usage-summary-card"),

		html.H3(
			html.Class("text-lg font-medium text-gray-900 dark:text-gray-100 mb-4"),
			gomponents.Text(locales["usage_summary"]),
		),

		html.Div(
			html.Class("space-y-3"),

			// Iterate through resource types in a consistent order
			gomponents.Group(
				gomponents.Map([]limits.ResourceType{
					limits.ResourcePlayers,
					limits.ResourceSeasons,
					limits.ResourceMatches,
					limits.ResourceTeams,
				}, func(resourceType limits.ResourceType) gomponents.Node {
					result, exists := usage[resourceType]
					if !exists {
						return gomponents.Text("")
					}

					// Calculate usage percentage
					var usagePercentage float64
					var usageText string

					if result.IsUnlimited {
						usageText = fmt.Sprintf("%d %s (unlimited)", result.CurrentCount, resourceType)
						usagePercentage = 0 // No progress bar for unlimited
					} else {
						usagePercentage = float64(result.CurrentCount) / float64(result.Limit) * 100
						usageText = fmt.Sprintf("%d/%d %s", result.CurrentCount, result.Limit, resourceType)
					}

					// Color coding based on usage
					var textColor, progressColor string
					if result.IsUnlimited {
						textColor = "text-green-600 dark:text-green-400"
						progressColor = "bg-green-500"
					} else if usagePercentage >= 100 {
						textColor = "text-red-600 dark:text-red-400"
						progressColor = "bg-red-500"
					} else if usagePercentage >= 80 {
						textColor = "text-orange-600 dark:text-orange-400"
						progressColor = "bg-orange-500"
					} else {
						textColor = "text-gray-600 dark:text-gray-400"
						progressColor = "bg-blue-500"
					}

					return html.Div(
						html.Class("flex items-center justify-between"),

						html.Span(
							html.Class(fmt.Sprintf("text-sm font-medium %s", textColor)),
							gomponents.Text(usageText),
						),

						// Progress bar (only for limited resources)
						gomponents.If(!result.IsUnlimited,
							html.Div(
								html.Class("flex-1 mx-3"),
								html.Div(
									html.Class("bg-gray-200 dark:bg-gray-700 rounded-full h-2"),
									html.Div(
										html.Class(fmt.Sprintf("h-2 rounded-full transition-all duration-300 %s", progressColor)),
										gomponents.Attr("style", fmt.Sprintf("width: %.1f%%", usagePercentage)),
									),
								),
							),
						),

						// Percentage text (only for limited resources)
						gomponents.If(!result.IsUnlimited,
							html.Span(
								html.Class("text-xs text-gray-500 dark:text-gray-400 ml-2"),
								gomponents.Text(fmt.Sprintf("%.0f%%", usagePercentage)),
							),
						),
					)
				}),
			),
		),
	)
}
