import { test, expect } from "./limitFixture";

test.describe("Subscription Limits", () => {
  test.describe("Limit Enforcement Testing", () => {
    test.describe("Players at 60% usage", () => {
      test.use({
        playerConfig: { playerCount: 12 } // 12/20 = 60%
      });

      test("allows adding players under 60% usage", async ({ 
        limitFixture
      }) => {
        const { page } = limitFixture;
        
        await page.goto("/app/players");
        await page.waitForLoadState("networkidle");
        // At 60% usage (12/20), should be able to click the add button successfully
        await page.click('[data-testid="add-new-player-btn"]');
        
        // Wait for navigation or modal content to appear
        await page.waitForLoadState("networkidle");
        
        // Should either navigate to /new page or show add player modal/form
        const isAddPlayerPage = currentUrl.includes('/players/new') || 
                               pageContent?.includes('Add') ||
                               pageContent?.includes('Create') ||
                               pageContent?.includes('Name') && pageContent?.includes('Email');
        
        expect(isAddPlayerPage).toBeTruthy();
        expect(pageContent?.length || 0).toBeGreaterThan(100);
      });
    });

    test.describe("Players at 80% usage", () => {
      test.use({
        playerConfig: { playerCount: 16 } // 16/20 = 80%
      });

      test("shows warning when near player limit (80%)", async ({ 
        limitFixture
      }) => {
        const { page } = limitFixture;
        
        await page.goto("/app/players");
        await page.waitForLoadState("networkidle");

        // Accept cookie consent if present
        try {
          await page.click('button:has-text("Accept")', { timeout: 2000 });
        } catch (e) {
          // Cookie consent might not be present or already accepted
        }

        // At 80% usage (16/20), should still be able to access add form
        await page.click('[data-testid="add-new-player-btn"]');
        
        await page.waitForLoadState("networkidle");
        
        // Should show add player functionality with potential warnings
        const currentUrl = page.url();
        const pageContent = await page.textContent('body');
        
        const hasAddPlayerContent = currentUrl.includes('/players/new') || 
                                   pageContent?.includes('Add') ||
                                   pageContent?.includes('Create');
        
        expect(hasAddPlayerContent).toBeTruthy();
        
        // Should contain warning about approaching limit (16/20)
        const hasWarning = pageContent?.includes('16') && pageContent?.includes('20');
        expect(hasWarning).toBeTruthy();
      });
    });

    test.describe("Players at 100% usage", () => {
      test.use({
        playerConfig: { playerCount: 20 } // 20/20 = 100%
      });

      test("enforces player limit at 100% usage", async ({ 
        limitFixture
      }) => {
        const { page } = limitFixture;
        
        await page.goto("/app/players");
        await page.waitForLoadState("networkidle");

        // Accept cookie consent if present
        try {
          await page.click('button:has-text("Accept")', { timeout: 2000 });
        } catch (e) {
          // Cookie consent might not be present or already accepted
        }

        // At 100% usage (20/20), clicking add should show limit enforcement
        await page.click('[data-testid="add-new-player-btn"]');
        
        // Either:
        // 1. Redirected to a limit page, or 
        // 2. Shows limit warning on the form, or
        // 3. Button is disabled/shows error
        
        await page.waitForLoadState("networkidle");
        
        const currentUrl = page.url();
        const pageContent = await page.textContent('body');
        
        // Should show evidence of limit enforcement
        const hasLimitMessage = pageContent?.includes('limit') || 
                               pageContent?.includes('20') ||
                               pageContent?.includes('maximum') ||
                               pageContent?.includes('upgrade') ||
                               currentUrl.includes('limit');
                               
        expect(hasLimitMessage).toBeTruthy();
        
        // Verify page actually loaded (not empty)
        expect(pageContent?.length || 0).toBeGreaterThan(50);
      });
    });

    test.describe("Teams at 80% usage", () => {
      test.use({
        teamConfig: { teamCount: 4 } // 4/5 = 80%
      });

      test("allows adding teams under limit (80%)", async ({ 
        limitFixture
      }) => {
        const { page } = limitFixture;
        
        await page.goto("/app/teams");
        await page.waitForLoadState("networkidle");

        // Accept cookie consent if present
        try {
          await page.click('button:has-text("Accept")', { timeout: 2000 });
        } catch (e) {
          // Cookie consent might not be present or already accepted
        }

        // At 80% usage (4/5), should still be able to add teams
        await page.click('[data-testid="add-new-team-btn"]');
        
        await page.waitForLoadState("networkidle");
        
        // Should reach team creation functionality
        const currentUrl = page.url();
        const pageContent = await page.textContent('body');
        
        const hasAddTeamContent = currentUrl.includes('/teams/new') || 
                                 pageContent?.includes('Team') ||
                                 pageContent?.includes('Add') ||
                                 pageContent?.includes('Create');
        
        expect(hasAddTeamContent).toBeTruthy();
        expect(pageContent?.length || 0).toBeGreaterThan(100);
      });
    });

    test.describe("Teams at 100% usage", () => {
      test.use({
        teamConfig: { teamCount: 5 } // 5/5 = 100%
      });

      test("enforces team limit at 100% usage", async ({ 
        limitFixture
      }) => {
        const { page } = limitFixture;
        
        await page.goto("/app/teams");
        await page.waitForLoadState("networkidle");

        // Accept cookie consent if present
        try {
          await page.click('button:has-text("Accept")', { timeout: 2000 });
        } catch (e) {
          // Cookie consent might not be present or already accepted
        }

        // At 100% usage (5/5), should enforce team limit
        await page.click('[data-testid="add-new-team-btn"]');
        
        await page.waitForLoadState("networkidle");
        
        const currentUrl = page.url();
        const pageContent = await page.textContent('body');
        
        // Should show evidence of limit enforcement for teams
        const hasLimitMessage = pageContent?.includes('limit') || 
                               pageContent?.includes('5') ||
                               pageContent?.includes('maximum') ||
                               pageContent?.includes('upgrade') ||
                               currentUrl.includes('limit');
                               
        expect(hasLimitMessage).toBeTruthy();
        expect(pageContent?.length || 0).toBeGreaterThan(50);
      });
    });

    test.describe("Seasons at 100% usage", () => {
      test.use({
        seasonConfig: { seasonCount: 3 }, // 3/3 = 100%
        playerConfig: { playerCount: 8 }
      });

      test("enforces season limit at 100% usage", async ({ 
        limitFixture
      }) => {
        const { page } = limitFixture;
        
        await page.goto("/app/seasons");
        await page.waitForLoadState("networkidle");

        // Accept cookie consent if present
        try {
          await page.click('button:has-text("Accept")', { timeout: 2000 });
        } catch (e) {
          // Cookie consent might not be present or already accepted
        }
        
        // Look for Add Season button and click it
        const addSeasonBtn = page.locator('[data-testid*="add-season"], [data-testid*="add-new-season"], button:has-text("Add Season")');
        if (await addSeasonBtn.count() > 0) {
          await addSeasonBtn.first().click();
        } else {
          // Try navigating directly to seasons/new
          await page.goto("/app/seasons/new");
        }
        
        await page.waitForLoadState("networkidle");
        
        const currentUrl = page.url();
        const pageContent = await page.textContent('body');
        
        // Should show evidence of limit enforcement for seasons (3/3)
        const hasLimitMessage = pageContent?.includes('limit') || 
                               pageContent?.includes('3') ||
                               pageContent?.includes('maximum') ||
                               pageContent?.includes('upgrade') ||
                               currentUrl.includes('limit');
                               
        expect(hasLimitMessage).toBeTruthy();
        expect(pageContent?.length || 0).toBeGreaterThan(50);
      });
    });
  });

  test.describe("Limit Enforcement", () => {
    test.describe("Player limit at 20/20", () => {
      test.use({
        playerConfig: { playerCount: 20 }
      });

      test("prevents creating 21st player when at 20/20 limit", async ({ 
        limitFixture
      }) => {
        const { page } = limitFixture;
        
        await page.goto("/app/players/new");
        await page.waitForLoadState("networkidle");
        
        // Try to create a new player
        await page.fill('#name', "Player 21");
        await page.fill('#email', "<EMAIL>");
        await page.click('button[type="submit"]');
        
        // Should see error toast
        const toast = page.locator('#toast-body-container');
        await expect(toast).toBeVisible();
        // Wait for toast content to appear
        await page.waitForTimeout(1000);
        const toastText = await toast.textContent();
        expect(toastText?.toLowerCase()).toContain('limit');
      });
    });

    test.describe("Season limit at 3/3", () => {
      test.use({
        seasonConfig: { seasonCount: 3 },
        playerConfig: { playerCount: 8 }
      });

      test("prevents creating 4th season when at 3/3 limit", async ({ 
        limitFixture
      }) => {
        const { page } = limitFixture;
        
        await page.goto("/app/seasons/new");
        await page.waitForLoadState("networkidle");
        
        // Try to create a new season
        await page.fill('#name', "Season 4");
        await page.click('button[type="submit"]');
        
        // Should see error toast
        const toast = page.locator('#toast-body-container');
        await expect(toast).toBeVisible();
        await page.waitForTimeout(1000);
        const toastText = await toast.textContent();
        expect(toastText?.toLowerCase()).toContain('limit');
      });
    });

    test.describe("Team limit at 5/5", () => {
      test.use({
        teamConfig: { teamCount: 5 }
      });

      test("prevents creating 6th team when at 5/5 limit", async ({ 
        limitFixture
      }) => {
        const { page } = limitFixture;
        
        await page.goto("/app/teams/new");
        await page.waitForLoadState("networkidle");
        
        // Try to create a new team
        await page.fill('#name', "Team 6");
        await page.click('button[type="submit"]');
        
        // Should see error toast
        const toast = page.locator('#toast-body-container');
        await expect(toast).toBeVisible();
        await page.waitForTimeout(1000);
        const toastText = await toast.textContent();
        expect(toastText?.toLowerCase()).toContain('limit');
      });
    });

    test.describe("Match limit near 100", () => {
      test.use({
        seasonConfig: { seasonCount: 1 },
        matchConfig: { matchCount: 99 }, // Setup to have 99/100 matches
        playerConfig: { playerCount: 15 } // Enough players that new season would create 2+ matches
      });

      test("prevents season creation when it would exceed match limit", async ({ 
        limitFixture
      }) => {
        const { page } = limitFixture;
        
        await page.goto("/app/seasons/new");
        await page.waitForLoadState("networkidle");
        
        // Try to create a season that would generate matches exceeding limit
        await page.fill('#name', "Season Exceeding Matches");
        
        // Select many players to ensure it would create multiple matches
        const playerCheckboxes = page.locator('input[type="checkbox"][name*="player"]');
        const checkboxCount = await playerCheckboxes.count();
        
        // Select enough players to generate matches that would exceed limit
        for (let i = 0; i < Math.min(checkboxCount, 10); i++) {
          await playerCheckboxes.nth(i).check();
        }
        
        await page.click('button[type="submit"]');
        
        // Should see error about match limit
        const toast = page.locator('#toast-body-container');
        await expect(toast).toBeVisible();
        await page.waitForTimeout(1000);
        const toastText = await toast.textContent();
        expect(toastText?.toLowerCase()).toContain('match');
      });
    });

    test.describe("Player import at capacity", () => {
      test.use({
        playerConfig: { playerCount: 19 } // 19/20, room for exactly 1 more
      });

      test("allows player import when within capacity", async ({ 
        limitFixture
      }) => {
        const { page } = limitFixture;
        
        await page.goto("/app/players/import");
        await page.waitForLoadState("networkidle");
        
        // Try to import exactly 1 player (should work)
        const csvData = "Name,Email\nPlayer 20,<EMAIL>";
        const textarea = page.locator('textarea[name="csvData"], #csvData, textarea');
        if (await textarea.count() > 0) {
          await textarea.fill(csvData);
          await page.click('button[type="submit"]');
          
          // Should succeed
          const toast = page.locator('#toast-body-container');
          await expect(toast).toBeVisible();
          await page.waitForTimeout(1000);
          const toastText = await toast.textContent();
          expect(toastText?.toLowerCase()).toContain('import');
        }
      });
    });

    test.describe("Player import over capacity", () => {
      test.use({
        playerConfig: { playerCount: 19 } // 19/20, room for exactly 1 more
      });

      test("prevents player import when over capacity", async ({ 
        limitFixture
      }) => {
        const { page } = limitFixture;
        
        await page.goto("/app/players/import");
        await page.waitForLoadState("networkidle");
        
        // Try to import 5 players (should fail - would exceed limit)
        const csvData = `Name,Email
Player 20,<EMAIL>
Player 21,<EMAIL>
Player 22,<EMAIL>
Player 23,<EMAIL>
Player 24,<EMAIL>`;
        
        const textarea = page.locator('textarea[name="csvData"], #csvData, textarea');
        if (await textarea.count() > 0) {
          await textarea.fill(csvData);
          await page.click('button[type="submit"]');
          
          // Should see error toast about exceeding limit
          const toast = page.locator('#toast-body-container');
          await expect(toast).toBeVisible();
          await page.waitForTimeout(1000);
          const toastText = await toast.textContent();
          expect(toastText?.toLowerCase()).toContain('limit');
        }
      });
    });
  });

  test.describe("Multiple Resource Limits", () => {
    test.describe("Multiple resources near limits", () => {
      test.use({
        playerConfig: { playerCount: 19 }, // 19/20 = 95%
        seasonConfig: { seasonCount: 3 },   // 3/3 = 100%
        teamConfig: { teamCount: 4 }      // 4/5 = 80%
      });

      test("shows multiple limit banners when near limits on multiple resources", async ({ 
        limitFixture
      }) => {
        const { page } = limitFixture;
        
        // Test on players page
        await page.goto("/app/players/new");
        await page.waitForLoadState("networkidle");
        
        const playersLimitBanner = page.locator('[data-testid*="usage"], .bg-blue-50, .bg-orange-50, .bg-red-50').first();
        await expect(playersLimitBanner).toBeVisible();
        
        // Test on seasons page
        await page.goto("/app/seasons/new");
        await page.waitForLoadState("networkidle");
        
        const seasonsLimitBanner = page.locator('[data-testid*="usage"], .bg-blue-50, .bg-orange-50, .bg-red-50').first();
        await expect(seasonsLimitBanner).toBeVisible();
        
        // Test on teams page
        await page.goto("/app/teams/new");
        await page.waitForLoadState("networkidle");
        
        const teamsLimitBanner = page.locator('[data-testid*="usage"], .bg-blue-50, .bg-orange-50, .bg-red-50').first();
        await expect(teamsLimitBanner).toBeVisible();
      });
    });

    test.describe("All resources at limit", () => {
      test.use({
        playerConfig: { playerCount: 20 }, // 20/20 = 100%
        seasonConfig: { seasonCount: 3 },   // 3/3 = 100%
        teamConfig: { teamCount: 5 }      // 5/5 = 100%
      });

      test("prevents resource creation when multiple resources are at limit", async ({ 
        limitFixture
      }) => {
        const { page } = limitFixture;
        
        // Try to create player - should fail
        await page.goto("/app/players/new");
        await page.waitForLoadState("networkidle");
        
        await page.fill('#name', "Player 21");
        await page.fill('#email', "<EMAIL>");
        await page.click('button[type="submit"]');
        
        let toast = page.locator('#toast-body-container');
        await expect(toast).toBeVisible();
        await page.waitForTimeout(1000);
        let toastText = await toast.textContent();
        expect(toastText?.toLowerCase()).toContain('limit');
        
        // Try to create season - should fail
        await page.goto("/app/seasons/new");
        await page.waitForLoadState("networkidle");
        
        await page.fill('#name', "Season 4");
        await page.click('button[type="submit"]');
        
        toast = page.locator('#toast-body-container');
        await expect(toast).toBeVisible();
        await page.waitForTimeout(1000);
        toastText = await toast.textContent();
        expect(toastText?.toLowerCase()).toContain('limit');
        
        // Try to create team - should fail
        await page.goto("/app/teams/new");
        await page.waitForLoadState("networkidle");
        
        await page.fill('#name', "Team 6");
        await page.click('button[type="submit"]');
        
        toast = page.locator('#toast-body-container');
        await expect(toast).toBeVisible();
        await page.waitForTimeout(1000);
        toastText = await toast.textContent();
        expect(toastText?.toLowerCase()).toContain('limit');
      });
    });
  });
});
